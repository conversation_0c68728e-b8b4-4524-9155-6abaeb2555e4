<?xml version="1.0" encoding="utf-8"?>
<packageDescription id="d0htg1xd">
  <resources>
    <image id="f2dp0" name="btnStart.png" path="/images/"/>
    <component id="p6dv1" name="LobbyPanel.xml" path="/" exported="true"/>
    <image id="m6gv2" name="levelBg.png" path="/images/" scale="9grid" scale9grid="29,24,14,21"/>
    <component id="g23932" name="HeartBar.xml" path="/"/>
    <component id="m6gv3" name="ProgressBar1.xml" path="/"/>
    <component id="ibx733" name="BuyHeartPanel.xml" path="/" exported="true"/>
    <image id="ibx734" name="btnClose.png" path="/images/"/>
    <component id="ibx735" name="btnBuy.xml" path="/"/>
    <component id="ibx736" name="btnAdd.xml" path="/"/>
    <component id="s20k37" name="ShopPanel.xml" path="/" exported="true"/>
    <component id="s20k38" name="ShopItem.xml" path="/"/>
    <component id="v8mf3g" name="MenuItem.xml" path="/"/>
    <component id="evzc3h" name="PigPanel.xml" path="/" exported="true"/>
    <image id="ss2k3i" name="bg.png" path="/images/" atlas="alone_npot"/>
    <image id="ss2k3j" name="btnSetting.png" path="/images/"/>
    <component id="ss2k3l" name="btnSetting.xml" path="/"/>
    <image id="ss2k3n" name="btnShop.png" path="/images/"/>
    <image id="ss2k3o" name="levelRewardBar.png" path="/images/"/>
    <image id="ss2k3p" name="pigBar.png" path="/images/"/>
    <image id="ss2k3q" name="progressBar.png" path="/images/"/>
    <image id="ss2k3r" name="progressBg.png" path="/images/" scale="9grid" scale9grid="15,0,3,26"/>
    <image id="ss2k3s" name="btnGet.png" path="/images/"/>
    <image id="ss2k3u" name="pigNumBg.png" path="/images/"/>
    <image id="ss2k3v" name="pigOnceBg.png" path="/images/"/>
    <image id="ss2k3w" name="pigProgressBar.png" path="/images/"/>
    <image id="ss2k3x" name="pigProgressBg.png" path="/images/"/>
    <image id="ss2k3y" name="pigTitle.png" path="/images/"/>
    <image id="ss2k3z" name="pigImg.png" path="/images/"/>
    <component id="ss2k40" name="pigProgressBar.xml" path="/"/>
    <image id="t5i141" name="123.png" path="/images/"/>
    <image id="t5i145" name="image_titleHp.png" path="/images/"/>
    <image id="t5i146" name="image_bg.png" path="/images/" scale="9grid" scale9grid="20,19,6,8"/>
    <image id="t5i147" name="image_line.png" path="/images/" scale="tile"/>
    <image id="t5i148" name="image_bg_green.png" path="/images/" scale="9grid" scale9grid="33,0,8,61"/>
    <image id="t5i14b" name="image_btnText1.png" path="/images/"/>
    <component id="t5i14c" name="BtnHeartBuy.xml" path="/"/>
    <image id="t5i14d" name="image_line2.png" path="/images/"/>
    <image id="t5i14e" name="image_line3.png" path="/images/"/>
    <component id="t5i14f" name="BtnHeartShare.xml" path="/"/>
    <image id="t5i14i" name="image_boxReward_arrow.png" path="/images/" duplicatePadding="true"/>
    <image id="t5i14j" name="image_boxReward_bg.png" path="/images/" scale="9grid" scale9grid="31,31,6,4"/>
    <component id="t5i14k" name="BoxRewardTip.xml" path="/"/>
    <component id="t5i14l" name="ItemReward.xml" path="/"/>
    <component id="t5tr4m" name="Heart.xml" path="/" exported="true"/>
    <misc id="hl2b5b" name="StartButton.atlas" path="/spine/"/>
    <image id="hl2b5d" name="StartButton.png" path="/spine/"/>
    <component id="hl2b5g" name="BtnStart.xml" path="/"/>
    <spine id="hl2b65" name="StartButton.skel" path="/spine/" width="375" height="128" require="hl2b5b,hl2b5d" atlasNames="StartButton" anchor="188,64" shader="FairyGUI/Image"/>
    <component id="hl2b66" name="ProgressBarPig.xml" path="/"/>
    <image id="hl2b67" name="prg_pig_bar.png" path="/images/"/>
    <image id="hl2b68" name="prg_pig_bg.png" path="/images/" scale="9grid" scale9grid="10,0,6,20"/>
    <misc id="eji269" name="majiangXM.atlas" path="/spine/"/>
    <image id="eji26a" name="majiangXM.png" path="/spine/"/>
    <spine id="eji26b" name="majiangXM.skel" path="/spine/" width="527" height="594" require="eji269,eji26a" atlasNames="majiangXM" anchor="265,453" shader="FairyGUI/Image"/>
  </resources>
  <publish name=""/>
</packageDescription>