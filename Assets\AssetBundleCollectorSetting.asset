%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 185f6993d5150494d98da50e26cb1c25, type: 3}
  m_Name: AssetBundleCollectorSetting
  m_EditorClassIdentifier: 
  ShowPackageView: 1
  EnableAddressable: 0
  LocationToLower: 0
  IncludeAssetGUID: 0
  UniqueBundleName: 0
  ShowEditorAlias: 0
  Packages:
  - PackageName: DefaultPackage
    PackageDesc: 
    Groups:
    - GroupName: Default Group
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/_MyGame/Bundles/DataSO
        CollectorGUID: 075ea2371857bb043bca2221e4e3b4ea
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Fonts
        CollectorGUID: e495910d2af44c8438550cfb9b62a255
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Prefabs
        CollectorGUID: bd0433640277d0343a3c01cbd6cbdaa8
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Scenes
        CollectorGUID: 665b08c819155614782213ad0cd3a885
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Shader
        CollectorGUID: cfc4332e7bf6df649937f92e2f59555a
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Sound
        CollectorGUID: 47ed4389a65622346b1a6bbe424b962b
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Textures
        CollectorGUID: 77cc23f2bd3276b4e8dec0500bef5abe
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/UI/FairyGUI
        CollectorGUID: 6ddfaa3c17e7cd049b426b094cbdd3ca
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackTopDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
