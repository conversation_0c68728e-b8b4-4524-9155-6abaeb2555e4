<?xml version="1.0" encoding="utf-8"?>
<component size="720,1280">
  <displayList>
    <image id="n20_ss2k" name="n20" src="ss2k3i" fileName="images/bg.png" xy="0,0">
      <relation target="" sidePair="width-width,height-height"/>
    </image>
    <loader3D id="n31_eji2" name="n31" xy="100,418" size="527,594" touchable="false" url="ui://d0htg1xdeji26b" animation="animation" skin="" loop="true">
      <relation target="" sidePair="center-center,middle-middle"/>
    </loader3D>
    <image id="n26_t5i1" name="n26" src="t5i142" fileName="images/logo.png" pkg="dd926aok" xy="216,260">
      <relation target="" sidePair="center-center,middle-middle"/>
    </image>
    <component id="n29_qlss" name="btnStart" src="hl2b5g" fileName="BtnStart.xml" xy="172,1020" group="n6_m6gv"/>
    <group id="n6_m6gv" name="n6" xy="172,1020" size="375,128" advanced="true">
      <relation target="" sidePair="center-center,bottom-bottom"/>
    </group>
    <component id="n25_t5i1" name="btnSetting" src="p6dvo" fileName="IconButton.xml" pkg="dzm3l2gb" xy="30,60" size="69,79" group="n13_m6gv">
      <Button icon="ui://d0htg1xdss2k3j"/>
    </component>
    <component id="n11_m6gv" name="coinBar" src="rckv2v" fileName="CoinBar.xml" pkg="dzm3l2gb" xy="453,96" group="n13_m6gv"/>
    <component id="n12_m6gv" name="heartBar" src="g23932" fileName="HeartBar.xml" xy="227,93" group="n13_m6gv"/>
    <group id="n13_m6gv" name="n13" xy="30,51" size="528,87" advanced="true">
      <relation target="" sidePair="left-left,top-top"/>
    </group>
    <component id="n18_aj2j" name="levelBar" src="eji24g" fileName="BtnBoxBar.xml" pkg="dzm3l2gb" xy="493,199">
      <relation target="" sidePair="center-center,top-top"/>
    </component>
    <component id="n19_evzc" name="btnPig" src="eji24p" fileName="BtnPig.xml" pkg="dzm3l2gb" xy="205,194" pivot="0.5,0.5" anchor="true" size="230,121">
      <relation target="" sidePair="center-center,top-top"/>
    </component>
    <text id="n30_tn6z" name="txtVersion" xy="26,18" size="206,34" visible="false" alpha="0.1" fontSize="20" color="#ffffff" autoSize="none" text="1.1.0"/>
  </displayList>
</component>