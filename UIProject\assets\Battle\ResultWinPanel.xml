<?xml version="1.0" encoding="utf-8"?>
<component size="720,1280">
  <displayList>
    <image id="n24_oak1" name="n24" src="jspmq" fileName="images/popBg.png" pkg="dzm3l2gb" xy="27,308" size="666,809" group="n30_oak1"/>
    <image id="n44_t5i1" name="n44" src="t5i15h" fileName="images/image_bgResult.png" xy="7,255" size="705,1001" group="n30_oak1"/>
    <text id="n45_t5i1" name="n45" xy="232,391" size="254,82" group="n30_oak1" fontSize="62" color="#813909" bold="true" text="恭喜通关"/>
    <image id="n58_o6rc" name="n58" src="o6rc7k" fileName="images/image_passTipsBg.png" xy="62,481" size="594,86" group="n30_oak1" visible="false"/>
    <image id="n36_oak1" name="n36" src="t5i15i" fileName="images/image_result_coin.png" xy="463,566" pivot="0.5,0.5" anchor="true" group="n30_oak1"/>
    <image id="n46_t5i1" name="n46" src="t5i15j" fileName="images/image_result_countBg.png" xy="410,619" group="n30_oak1"/>
    <text id="n37_oak1" name="txtGoldCount" xy="405,618" pivot="0.5,0.5" size="122,42" group="n30_oak1" fontSize="30" color="#ffffff" align="center" vAlign="middle" autoSize="shrink" bold="true" shadowColor="#dd6800" shadowOffset="1,2" singleLine="true" autoClearText="true" text="10"/>
    <component id="n43_cqln" name="btnGet" src="bvh47o" fileName="btnComAd.xml" xy="358,823" pivot="0.5,0.5" anchor="true" group="n30_oak1">
      <Button icon="ui://dzm3l2gbt5i142"/>
    </component>
    <component id="n34_oak1" name="btnNext" src="s4ex7c" fileName="btnCom.xml" xy="357,954" pivot="0.5,0.5" anchor="true" size="411,125" group="n30_oak1">
      <Button title="&#xA;" icon="ui://kzjqd5ahmxgl7t"/>
      <property target="bg" propertyId="1" value="ui://kzjqd5ahbvh47m"/>
    </component>
    <component id="n31_oak1" name="btnClose" src="p6dvo" fileName="IconButton.xml" pkg="dzm3l2gb" xy="602,301" size="58,62" group="n30_oak1" visible="false">
      <Button icon="ui://kzjqd5ahjspm1j"/>
    </component>
    <image id="n50_s9fh" name="n50" src="s9fh5v" fileName="images/image_result_star.png" xy="161,497" group="n30_oak1"/>
    <image id="n51_s9fh" name="n51" src="s9fh5u" fileName="images/image_result_arrow.png" xy="338,551" group="n30_oak1"/>
    <image id="n52_s9fh" name="n52" src="t5i15j" fileName="images/image_result_countBg.png" xy="180,620" group="n30_oak1"/>
    <text id="n53_s9fh" name="txtStarCount" xy="175,619" pivot="0.5,0.5" size="122,42" group="n30_oak1" fontSize="30" color="#ffffff" align="center" vAlign="middle" autoSize="shrink" bold="true" shadowColor="#dd6800" shadowOffset="1,2" singleLine="true" autoClearText="true" text="10"/>
    <text id="n57_o6rc" name="n57" xy="126,692" size="463,44" group="n30_oak1" fontSize="32" color="#813909" align="center" text="局内获得的星星将转换为金币哦!"/>
    <text id="n59_o6rc" name="lblPassPercent" xy="93,485" size="531,75" group="n30_oak1" visible="false" fontSize="32" color="#813909" align="center" vAlign="middle" ubb="true" autoSize="shrink" bold="true" singleLine="true" autoClearText="true" text="您已击败全国[size=36]98%[/size]的麻友!"/>
    <group id="n30_oak1" name="n30" xy="7,255" size="705,1001" advanced="true">
      <relation target="" sidePair="center-center,middle-middle"/>
    </group>
    <component id="n56_ru7z" name="coinBar" src="rckv2v" fileName="CoinBar.xml" pkg="dzm3l2gb" xy="177,75" visible="false" touchable="false">
      <relation target="" sidePair="center-center,middle-middle"/>
    </component>
    <component id="n62_eji2" name="btnPig" src="eji24p" fileName="BtnPig.xml" pkg="dzm3l2gb" xy="77,127" group="n63_eji2"/>
    <component id="n61_eji2" name="levelBar" src="eji24g" fileName="BtnBoxBar.xml" pkg="dzm3l2gb" xy="483,189" group="n63_eji2"/>
    <group id="n63_eji2" name="boxBtn" xy="77,127" size="561,120" visible="false" advanced="true" layout="hz" colGap="20" excludeInvisibles="true">
      <relation target="" sidePair="center-center,middle-middle"/>
    </group>
    <loader3D id="n60_hl2b" name="n60" xy="60,264" size="600,300" touchable="false" url="ui://kzjqd5ahhl2b87" align="center" vAlign="middle" fill="scale" animation="animation" skin="" loop="true">
      <relation target="" sidePair="center-center,middle-middle"/>
    </loader3D>
  </displayList>
  <transition name="happy" autoPlay="true" autoPlayRepeat="-1"/>
  <transition name="sad" autoPlay="true" autoPlayRepeat="-1"/>
  <transition name="t2" autoPlay="true"/>
</component>