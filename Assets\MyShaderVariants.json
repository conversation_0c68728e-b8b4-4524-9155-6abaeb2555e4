{"ShaderTotalCount": 22, "VariantTotalCount": 35, "ShaderVariantInfos": [{"AssetPath": "Resources/unity_builtin_extra", "ShaderName": "Hidden/BlitCopy", "ShaderVariantCount": 1, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}]}, {"AssetPath": "Resources/unity_builtin_extra", "ShaderName": "Skybox/Procedural", "ShaderVariantCount": 1, "ShaderVariantElements": [{"PassType": 0, "Keywords": ["_SUNDISK_SIMPLE"]}]}, {"AssetPath": "Resources/unity_builtin_extra", "ShaderName": "Hidden/Internal-GUITextureClip", "ShaderVariantCount": 1, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}]}, {"AssetPath": "Resources/unity_builtin_extra", "ShaderName": "Hidden/Internal-GUITextureClipText", "ShaderVariantCount": 1, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}]}, {"AssetPath": "Resources/unity_builtin_extra", "ShaderName": "Hidden/Internal-GUITexture", "ShaderVariantCount": 1, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}]}, {"AssetPath": "Resources/unity_builtin_extra", "ShaderName": "Hidden/Internal-GUITextureBlit", "ShaderVariantCount": 1, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}]}, {"AssetPath": "Resources/unity_builtin_extra", "ShaderName": "Hidden/Internal-GUIRoundedRect", "ShaderVariantCount": 1, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}]}, {"AssetPath": "Resources/unity_builtin_extra", "ShaderName": "Hidden/Internal-GUIRoundedRectWithColorPerBorder", "ShaderVariantCount": 1, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}]}, {"AssetPath": "Resources/unity_builtin_extra", "ShaderName": "Hidden/Internal-UIRAtlasBlitCopy", "ShaderVariantCount": 1, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}]}, {"AssetPath": "Resources/unity_builtin_extra", "ShaderName": "Hidden/UIElements/EditorUIE", "ShaderVariantCount": 1, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}]}, {"AssetPath": "Resources/unity_builtin_extra", "ShaderName": "Mobile/Particles/Additive", "ShaderVariantCount": 1, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}]}, {"AssetPath": "Packages/com.unity.render-pipelines.universal/Shaders/Utils/CopyDepth.shader", "ShaderName": "Hidden/Universal Render Pipeline/CopyDepth", "ShaderVariantCount": 1, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}]}, {"AssetPath": "Packages/com.unity.render-pipelines.universal/Shaders/PostProcessing/LutBuilderLdr.shader", "ShaderName": "Hidden/Universal Render Pipeline/LutBuilderLdr", "ShaderVariantCount": 1, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}]}, {"AssetPath": "Packages/com.unity.render-pipelines.universal/Shaders/PostProcessing/UberPost.shader", "ShaderName": "Hidden/Universal Render Pipeline/UberPost", "ShaderVariantCount": 1, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}]}, {"AssetPath": "Packages/com.unity.render-pipelines.universal/Shaders/Lit.shader", "ShaderName": "Universal Render Pipeline/Lit", "ShaderVariantCount": 4, "ShaderVariantElements": [{"PassType": 8, "Keywords": [""]}, {"PassType": 13, "Keywords": [""]}, {"PassType": 13, "Keywords": ["_ADDITIONAL_LIGHT_SHADOWS", "_MAIN_LIGHT_SHADOWS"]}, {"PassType": 13, "Keywords": ["_MAIN_LIGHT_SHADOWS"]}]}, {"AssetPath": "Packages/com.unity.render-pipelines.universal/Shaders/Particles/ParticlesUnlit.shader", "ShaderName": "Universal Render Pipeline/Particles/Unlit", "ShaderVariantCount": 3, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}, {"PassType": 0, "Keywords": ["_COLORADDSUBDIFF_ON", "_SURFACE_TYPE_TRANSPARENT"]}, {"PassType": 0, "Keywords": ["_SURFACE_TYPE_TRANSPARENT"]}]}, {"AssetPath": "Assets/_MyGame/RawRes/Material/MaJiang.shadergraph", "ShaderName": "Shader Graphs/Ma<PERSON>iang", "ShaderVariantCount": 4, "ShaderVariantElements": [{"PassType": 8, "Keywords": [""]}, {"PassType": 13, "Keywords": [""]}, {"PassType": 13, "Keywords": ["_ADDITIONAL_LIGHT_SHADOWS", "_MAIN_LIGHT_SHADOWS"]}, {"PassType": 13, "Keywords": ["_MAIN_LIGHT_SHADOWS"]}]}, {"AssetPath": "Assets/Spine/Runtime/spine-unity/Shaders/BlendModes/Spine-Skeleton-PMA-Multiply.shader", "ShaderName": "Spine/Blend Modes/Skeleton PMA Multiply", "ShaderVariantCount": 2, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}, {"PassType": 8, "Keywords": ["SHADOWS_DEPTH"]}]}, {"AssetPath": "Assets/Spine/Runtime/spine-unity/Shaders/BlendModes/Spine-Skeleton-PMA-Screen.shader", "ShaderName": "Spine/Blend Modes/Skeleton PMA Screen", "ShaderVariantCount": 2, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}, {"PassType": 8, "Keywords": ["SHADOWS_DEPTH"]}]}, {"AssetPath": "Packages/com.unity.render-pipelines.universal/Shaders/Unlit.shader", "ShaderName": "Universal Render Pipeline/Unlit", "ShaderVariantCount": 2, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}, {"PassType": 0, "Keywords": ["_SURFACE_TYPE_TRANSPARENT"]}]}, {"AssetPath": "Assets/Spine/Runtime/spine-unity/Shaders/Spine-Skeleton.shader", "ShaderName": "Spine/Skeleton", "ShaderVariantCount": 2, "ShaderVariantElements": [{"PassType": 0, "Keywords": ["_STRAIGHT_ALPHA_INPUT"]}, {"PassType": 8, "Keywords": ["SHADOWS_DEPTH"]}]}, {"AssetPath": "Assets/Spine/Runtime/spine-unity/Shaders/BlendModes/Spine-Skeleton-PMA-Additive.shader", "ShaderName": "Spine/Blend Modes/Skeleton PMA Additive", "ShaderVariantCount": 2, "ShaderVariantElements": [{"PassType": 0, "Keywords": [""]}, {"PassType": 8, "Keywords": ["SHADOWS_DEPTH"]}]}]}