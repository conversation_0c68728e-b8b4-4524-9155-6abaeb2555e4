<?xml version="1.0" encoding="utf-8"?>
<component size="720,1280" opaque="false" designImage="ui://kzjqd5aho6rc7i" designImageAlpha="100">
  <displayList>
    <graph id="n45_k5ut" name="mask" xy="-250,-152" size="1236,1635" visible="false" type="rect" fillColor="#7f000000">
      <relation target="" sidePair="width-width,height-height"/>
    </graph>
    <image id="n37_ss2k" name="n37" src="ss2k43" fileName="images/imgTop.png" xy="0,0" size="720,82">
      <relation target="" sidePair="width-width,top-top"/>
    </image>
    <image id="n33_ss2k" name="n33" src="ss2k42" fileName="images/imgBottom.png" xy="0,1091" size="720,189">
      <relation target="" sidePair="width-width,bottom-bottom"/>
    </image>
    <graph id="n6_p6dv" name="touchMask" xy="0,1082" size="720,198" type="rect" lineSize="0" fillColor="#00ffffff">
      <relation target="" sidePair="width-width,bottom-bottom"/>
    </graph>
    <graph id="n11_k64y" name="n11" xy="16,974" size="689,136" group="n12_k64y" type="rect" fillColor="#cc333333" corner="15"/>
    <component id="n8_k64y" name="btnCreate" src="9mk633" fileName="btnCreate.xml" xy="588,997" size="98,88" group="n12_k64y">
      <Button title="执行"/>
    </component>
    <graph id="n14_k64y" name="n14" xy="44,991" size="316,55" group="n12_k64y" type="rect" fillColor="#ff999999" corner="45"/>
    <text id="n10_k64y" name="txtNum" xy="61,999" size="284,39" group="n12_k64y" fontSize="20" color="#ffffff" vAlign="middle" autoSize="none" text="" input="true" prompt="麻将数字,数字,数字"/>
    <graph id="n15_k64y" name="n15" xy="374,991" size="184,55" group="n12_k64y" type="rect" fillColor="#ff999999" corner="45"/>
    <text id="n16_k64y" name="txtDelay" xy="391,999" size="149,39" group="n12_k64y" fontSize="20" color="#ffffff" vAlign="middle" autoSize="none" text="" input="true" prompt="间隔时间（秒）"/>
    <group id="n12_k64y" name="n12" xy="16,974" size="689,136" visible="false" advanced="true">
      <relation target="" sidePair="center-center,bottom-bottom"/>
    </group>
    <component id="n19_mr9u" name="leftTimeBar" src="mr9u36" fileName="LeftTimeBar.xml" xy="25,61">
      <relation target="" sidePair="left-left,top-top"/>
    </component>
    <image id="n38_ss2k" name="n38" src="ss2k44" fileName="images/levelBg.png" xy="251,82" size="135,44" group="n25_oak1"/>
    <text id="n24_oak1" name="txtLevel" xy="260,84" size="117,39" group="n25_oak1" fontSize="28" color="#ffffff" align="center" vAlign="middle" autoSize="shrink" bold="true" singleLine="true" autoClearText="true" text="关卡20"/>
    <group id="n25_oak1" name="n25" xy="251,82" size="135,44" advanced="true">
      <relation target="" sidePair="left-left,top-top"/>
    </group>
    <component id="n18_k64y" name="btnBulb" src="k64y35" fileName="btnItem.xml" xy="162,1192" size="138,128" group="n32_g14v">
      <Button title="消除" icon="ui://dzm3l2gbgivr16"/>
    </component>
    <component id="n29_g14v" name="btnShuffle" src="k64y35" fileName="btnItem.xml" xy="360,1192" size="138,128" group="n32_g14v">
      <Button title="洗牌" icon="ui://dzm3l2gbgivr17"/>
    </component>
    <component id="n30_g14v" name="btnTurn" src="k64y35" fileName="btnItem.xml" xy="558,1192" size="138,128" group="n32_g14v">
      <Button title="翻牌" icon="ui://dzm3l2gbgivr18"/>
    </component>
    <group id="n32_g14v" name="n32" xy="93,1128" size="534,128" advanced="true" layout="hz" colGap="60">
      <relation target="" sidePair="center-center,bottom-bottom"/>
    </group>
    <component id="n21_mr9u" name="starBar" src="mr9u37" fileName="StarBar.xml" xy="400,76">
      <relation target="" sidePair="left-left,top-top"/>
    </component>
    <component id="n40_t5i1" name="comboTip" src="t5i156" fileName="Combo.xml" xy="570,856" visible="false" touchable="false">
      <relation target="" sidePair="middle-middle,right-right"/>
    </component>
    <loader3D id="n41_ikp8" name="itemEffect" xy="0,0" size="720,1280" visible="false" touchable="false" align="center" vAlign="middle" animation="daiji" skin="" loop="true">
      <relation target="" sidePair="center-center,middle-middle"/>
    </loader3D>
    <component id="n42_gb5q" name="guideTip" src="qoqy1p" fileName="TapMsg.xml" xy="359,233" visible="false" touchable="false">
      <relation target="" sidePair="center-center,top-top"/>
    </component>
    <component id="n22_lia5" name="comboBar" src="lia539" fileName="ComboBar.xml" xy="158,1085">
      <relation target="" sidePair="center-center,bottom-bottom"/>
      <ProgressBar value="50" max="100"/>
    </component>
    <image id="n47_so5q" name="n47" src="ss2k44" fileName="images/levelBg.png" xy="202,142" size="315,44" group="n48_so5q"/>
    <text id="n43_qto6" name="txtInfo" xy="208,144" size="301,39" group="n48_so5q" fontSize="28" color="#ffff00" align="center" vAlign="middle" autoSize="shrink" bold="true" singleLine="true" autoClearText="true" text="种类 100    总数 100"/>
    <group id="n48_so5q" name="n48" xy="202,142" size="315,44" advanced="true">
      <relation target="" sidePair="center-center,top-top"/>
    </group>
    <component id="n44_e4hz" name="tipItem" src="iz8v60" fileName="ItemTips.xml" xy="163,1119" visible="false"/>
    <text id="n46_m6bg" name="txtChannel" xy="648,1242" size="33,34" alpha="0.05" fontSize="24" color="#ffffff" align="center" vAlign="middle" autoSize="shrink" bold="true" singleLine="true" text="A">
      <relation target="" sidePair="right-right,bottom-bottom"/>
    </text>
    <text id="n49_tn6z" name="txtVersion" xy="26,18" size="206,34" visible="false" alpha="0.1" fontSize="20" color="#ffffff" autoSize="none" text="1.1.0"/>
    <component id="n50_i26m" name="btnGm" src="i26m7z" fileName="BtnTestMenu.xml" xy="29,148" visible="false">
      <relation target="" sidePair="left-left,top-top"/>
    </component>
  </displayList>
</component>